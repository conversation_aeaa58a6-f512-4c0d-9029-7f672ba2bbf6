import 'package:flutter_test/flutter_test.dart';
import 'package:dasso_reader/service/dictionary/online_dictionary_service.dart';
import 'package:dasso_reader/service/dictionary/quick_tts_service.dart';

void main() {
  group('Pronunciation Functionality Tests', () {
    late OnlineDictionaryService onlineDictionaryService;
    late QuickTtsService quickTtsService;

    setUp(() {
      onlineDictionaryService = OnlineDictionaryService();
      quickTtsService = QuickTtsService();
    });

    test('OnlineDictionaryService should be initialized', () {
      expect(onlineDictionaryService, isNotNull);
    });

    test('QuickTtsService should be initialized', () {
      expect(quickTtsService, isNotNull);
    });

    test('QuickTtsService should check system TTS availability', () {
      // This test checks if the system TTS availability check works
      final isAvailable = quickTtsService.isSystemTtsAvailable;
      expect(isAvailable, isA<bool>());
    });

    test('OnlineDictionaryService should handle empty text', () async {
      final result = await onlineDictionaryService.playPronunciation('');
      expect(result, false);
    });

    test('OnlineDictionaryService should handle long text', () async {
      final longText = '这是一个非常长的文本，用来测试系统是否能够正确处理超过限制长度的文本内容，应该返回false';
      final result = await onlineDictionaryService.playPronunciation(longText);
      expect(result, false);
    });

    test('OnlineDictionaryService should handle Chinese characters', () async {
      // Note: This test will attempt actual TTS, so it may fail in CI environment
      // In a real test environment, you might want to mock the audio player
      final result = await onlineDictionaryService.playPronunciation('你好');
      expect(result, isA<bool>());
    });

    test('QuickTtsService should handle empty text', () async {
      final result = await quickTtsService.playSystemTts('');
      expect(result, false);
    });
  });
}
