import 'package:dasso_reader/service/dictionary/online_dictionary_service.dart';
import 'package:dasso_reader/service/dictionary/quick_tts_service.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';

/// Debug utility for testing pronunciation functionality
class PronunciationDebug {
  static final OnlineDictionaryService _onlineService = OnlineDictionaryService();
  static final QuickTtsService _quickService = QuickTtsService();

  /// Test pronunciation with various Chinese characters/words
  static Future<void> testPronunciation(BuildContext context) async {
    final testWords = ['你好', '中文', '学习', '发音', '测试'];
    
    AnxLog.info('Starting pronunciation debug test');
    
    for (final word in testWords) {
      AnxLog.info('Testing pronunciation for: $word');
      
      try {
        final result = await _onlineService.playPronunciation(word, context: context);
        AnxLog.info('Pronunciation result for "$word": $result');
        
        // Wait a bit between tests
        await Future.delayed(const Duration(seconds: 2));
      } catch (e) {
        AnxLog.severe('Error testing pronunciation for "$word": $e');
      }
    }
    
    AnxLog.info('Pronunciation debug test completed');
  }

  /// Test system TTS availability
  static void testSystemTtsAvailability() {
    AnxLog.info('Testing system TTS availability');
    
    final isAvailable = _quickService.isSystemTtsAvailable;
    AnxLog.info('System TTS available: $isAvailable');
  }

  /// Test system TTS pronunciation
  static Future<void> testSystemTts(BuildContext context, String text) async {
    AnxLog.info('Testing system TTS for: $text');
    
    try {
      final result = await _quickService.playSystemTts(text, context: context);
      AnxLog.info('System TTS result for "$text": $result');
    } catch (e) {
      AnxLog.severe('Error testing system TTS for "$text": $e');
    }
  }

  /// Show debug information in a dialog
  static void showDebugDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pronunciation Debug'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('System TTS Available: ${_quickService.isSystemTtsAvailable}'),
            const SizedBox(height: 16),
            const Text('Test Options:'),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => testPronunciation(context),
              child: const Text('Test Online TTS'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => testSystemTts(context, '你好'),
              child: const Text('Test System TTS'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
