import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:io' show Platform;

/// Isolated Quick TTS service for context menu pronunciation
/// Completely separate from the main reading TTS to prevent interference
class QuickTtsService {
  static final QuickTtsService _instance = QuickTtsService._internal();
  factory QuickTtsService() => _instance;
  QuickTtsService._internal();

  // Isolated FlutterTts instance - completely separate from main reading TTS
  FlutterTts? _isolatedTts;
  bool _isInitialized = false;
  bool _isPlaying = false;

  /// Initialize the isolated TTS instance
  Future<void> _initializeIsolatedTts() async {
    if (_isInitialized) return;

    try {
      _isolatedTts = FlutterTts();

      // Configure for quick pronunciation only - no reading flow handlers
      await _isolatedTts!.awaitSpeakCompletion(true);

      // Set completion handler to clean up state only
      _isolatedTts!.setCompletionHandler(() {
        _isPlaying = false;
        AnxLog.info('Quick pronunciation completed');
      });

      // Set error handler
      _isolatedTts!.setErrorHandler((msg) {
        _isPlaying = false;
        AnxLog.warning('Quick pronunciation error: $msg');
      });

      _isInitialized = true;
      AnxLog.info('Isolated TTS initialized successfully');
    } catch (e) {
      AnxLog.severe('Failed to initialize isolated TTS: $e');
      _isInitialized = false;
    }
  }

  /// Play pronunciation using isolated system TTS
  Future<bool> playSystemTts(String text, {BuildContext? context}) async {
    if (text.isEmpty) {
      AnxLog.warning('Empty text provided for quick pronunciation');
      return false;
    }

    try {
      // Initialize if needed
      await _initializeIsolatedTts();

      if (_isolatedTts == null) {
        AnxLog.severe('Isolated TTS not available');
        return false;
      }

      AnxLog.info('Playing isolated TTS for: "$text"');

      // Stop any ongoing pronunciation
      if (_isPlaying) {
        await _isolatedTts!.stop();
        _isPlaying = false;
      }

      // Set TTS parameters for good pronunciation
      await _isolatedTts!.setLanguage('zh-CN');
      await _isolatedTts!.setSpeechRate(0.8); // Slightly slower for clarity
      await _isolatedTts!.setVolume(1.0);
      await _isolatedTts!.setPitch(1.0);

      // Speak the text - this is completely isolated from reading flow
      _isPlaying = true;
      await _isolatedTts!.speak(text);

      AnxLog.info('Isolated TTS playback initiated successfully');
      return true;
    } catch (e) {
      _isPlaying = false;
      AnxLog.severe('Isolated TTS failed: $e');

      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Pronunciation error: ${e.toString()}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return false;
    }
  }

  /// Check if isolated system TTS is available
  bool get isSystemTtsAvailable {
    try {
      // Check if platform supports TTS
      return Platform.isAndroid || Platform.isIOS || Platform.isWindows;
    } catch (e) {
      AnxLog.warning('System TTS not available: $e');
      return false;
    }
  }

  /// Stop any ongoing pronunciation (isolated)
  Future<void> stop() async {
    try {
      if (_isolatedTts != null && _isPlaying) {
        await _isolatedTts!.stop();
        _isPlaying = false;
        AnxLog.info('Isolated TTS stopped');
      }
    } catch (e) {
      AnxLog.warning('Error stopping isolated TTS: $e');
    }
  }

  /// Check if currently playing pronunciation
  bool get isPlaying => _isPlaying;

  /// Dispose the isolated TTS instance
  Future<void> dispose() async {
    try {
      if (_isolatedTts != null) {
        await _isolatedTts!.stop();
        _isolatedTts = null;
        _isInitialized = false;
        _isPlaying = false;
        AnxLog.info('Isolated TTS disposed');
      }
    } catch (e) {
      AnxLog.warning('Error disposing isolated TTS: $e');
    }
  }
}
