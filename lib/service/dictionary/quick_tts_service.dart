import 'package:dasso_reader/service/tts/tts_factory.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';

/// Quick TTS service for context menu pronunciation
/// Integrates with existing TTS factory for system TTS fallback
class QuickTtsService {
  static final QuickTtsService _instance = QuickTtsService._internal();
  factory QuickTtsService() => _instance;
  QuickTtsService._internal();

  final TtsFactory _ttsFactory = TtsFactory();

  /// Play pronunciation using system TTS
  Future<bool> playSystemTts(String text, {BuildContext? context}) async {
    if (text.isEmpty) {
      AnxLog.warning('Empty text provided for system TTS');
      return false;
    }

    try {
      AnxLog.info('Playing system TTS for: "$text"');

      // Get current TTS instance from factory
      final tts = _ttsFactory.current;

      // Speak the text directly without the full reading flow
      await tts.speak(content: text);

      AnxLog.info('System TTS playback initiated successfully');
      return true;
    } catch (e) {
      AnxLog.severe('System TTS failed: $e');

      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('System TTS error: ${e.toString()}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return false;
    }
  }

  /// Check if system TTS is available
  bool get isSystemTtsAvailable {
    try {
      _ttsFactory.current;
      // Simply return true if we can get the TTS instance
      return true;
    } catch (e) {
      AnxLog.warning('System TTS not available: $e');
      return false;
    }
  }

  /// Stop any ongoing TTS playback
  Future<void> stop() async {
    try {
      final tts = _ttsFactory.current;
      await tts.stop();
    } catch (e) {
      AnxLog.warning('Error stopping TTS: $e');
    }
  }
}
