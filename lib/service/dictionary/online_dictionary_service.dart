import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:just_audio/just_audio.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/service/dictionary/quick_tts_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

/// Service for handling online dictionary lookups and pronunciation
class OnlineDictionaryService {
  static final OnlineDictionaryService _instance =
      OnlineDictionaryService._internal();
  factory OnlineDictionaryService() => _instance;

  OnlineDictionaryService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // HTTP client
  final http.Client _httpClient = http.Client();

  // Cache for dictionary lookups to reduce API calls
  final Map<String, DictionaryEntry> _dictionaryCache = {};
  final Map<String, CharacterInfo> _characterInfoCache = {};

  // Persistent cache keys
  static const String _dictionaryCacheKey = 'dictionary_cache';
  static const String _characterInfoCacheKey = 'character_info_cache';

  // Flag to track if we're in offline mode
  bool _isOfflineMode = false;

  /// Initialize the service and load cached data
  Future<void> initialize() async {
    await _loadCachedData();
  }

  /// Load cached dictionary and character info from SharedPreferences
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load dictionary cache
      final dictionaryJson = prefs.getString(_dictionaryCacheKey);
      if (dictionaryJson != null) {
        final data = json.decode(dictionaryJson) as Map<String, dynamic>;
        data.forEach((key, value) {
          if (value is Map<String, dynamic>) {
            _dictionaryCache[key] = DictionaryEntry.fromMap(value);
          }
        });
        AnxLog.info(
          'Loaded ${_dictionaryCache.length} dictionary entries from cache',
        );
      }

      // Load character info cache
      final characterInfoJson = prefs.getString(_characterInfoCacheKey);
      if (characterInfoJson != null) {
        final data = json.decode(characterInfoJson) as Map<String, dynamic>;
        data.forEach((key, value) {
          if (value is Map<String, dynamic>) {
            _characterInfoCache[key] = CharacterInfo.fromMap(value);
          }
        });
        AnxLog.info(
          'Loaded ${_characterInfoCache.length} character info entries from cache',
        );
      }
    } catch (e) {
      AnxLog.severe('Error loading cached data: $e');
    }
  }

  /// Save cached data to SharedPreferences
  Future<void> _saveCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save dictionary cache (limit to 1000 entries to avoid storage issues)
      final Map<String, dynamic> dictionaryData = {};
      final entries = _dictionaryCache.entries.take(1000).toList();
      for (final entry in entries) {
        dictionaryData[entry.key] = entry.value.toMap();
      }
      await prefs.setString(_dictionaryCacheKey, json.encode(dictionaryData));

      // Save character info cache
      final Map<String, dynamic> characterInfoData = {};
      for (final entry in _characterInfoCache.entries) {
        characterInfoData[entry.key] = entry.value.toMap();
      }
      await prefs.setString(
        _characterInfoCacheKey,
        json.encode(characterInfoData),
      );

      AnxLog.info(
        'Saved ${dictionaryData.length} dictionary entries and ${characterInfoData.length} character info entries to cache',
      );
    } catch (e) {
      AnxLog.severe('Error saving cached data: $e');
    }
  }

  /// Set offline mode
  void setOfflineMode(bool isOffline) {
    _isOfflineMode = isOffline;
    AnxLog.info('Dictionary service offline mode set to: $_isOfflineMode');
  }

  /// Play pronunciation for a Chinese word or character
  ///
  /// TTS Priority Order (optimized for China accessibility):
  /// 1. System TTS (works everywhere, including China)
  /// 2. Microsoft Edge TTS (accessible in China)
  /// 3. Google Translate TTS (blocked in China without VPN)
  Future<bool> playPronunciation(String text, {BuildContext? context}) async {
    if (text.isEmpty) {
      AnxLog.warning('Empty text provided for pronunciation');
      return false;
    }

    // Clean the text - remove extra whitespace and limit length
    final cleanText = text.trim();
    if (cleanText.length > 50) {
      AnxLog.warning(
        'Text too long for pronunciation: ${cleanText.length} characters',
      );
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Text too long for pronunciation'),
            duration: Duration(seconds: 2),
          ),
        );
      }
      return false;
    }

    if (_isOfflineMode) {
      AnxLog.info('Offline mode - attempting system TTS fallback');
      return await _trySystemTtsFallback(cleanText, context);
    }

    AnxLog.info('Playing pronunciation for: "$cleanText"');

    try {
      // Try primary service: System TTS (works everywhere, including China)
      if (context != null && context.mounted) {
        if (await _trySystemTtsFallback(cleanText, context)) {
          return true;
        }
      } else {
        if (await _trySystemTtsFallback(cleanText, null)) {
          return true;
        }
      }

      // Try secondary service: Microsoft Edge TTS (works in China)
      if (await _tryEdgeTts(cleanText)) {
        return true;
      }

      // Try final fallback: Google Translate TTS (blocked in China without VPN)
      if (await _tryGoogleTranslateTts(cleanText)) {
        return true;
      }

      // If all services fail, return false
      AnxLog.warning('All TTS services failed for text: "$cleanText"');
      return false;
    } catch (e) {
      AnxLog.severe('Error in pronunciation playback: $e');
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error playing pronunciation: ${e.toString()}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return false;
    }
  }

  /// Try Google Translate TTS service (blocked in China without VPN)
  Future<bool> _tryGoogleTranslateTts(String text) async {
    try {
      // Encode text for URL
      final encodedText = Uri.encodeComponent(text);
      final url =
          'https://translate.google.com/translate_tts?ie=UTF-8&q=$encodedText&tl=zh-CN&client=tw-ob&ttsspeed=1';

      AnxLog.info('Trying Google Translate TTS (final fallback): $url');

      // Set user agent to avoid blocking
      await _audioPlayer.setAudioSource(
        AudioSource.uri(
          Uri.parse(url),
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://translate.google.com/',
          },
        ),
      );

      await _audioPlayer.play();
      AnxLog.info('Google Translate TTS playback successful');
      return true;
    } catch (e) {
      AnxLog.info('Google Translate TTS failed (expected in China): $e');
      return false;
    }
  }

  /// Try Microsoft Edge TTS service (works in China)
  Future<bool> _tryEdgeTts(String text) async {
    try {
      // Use Microsoft Edge TTS - accessible in China
      final encodedText = Uri.encodeComponent(text);

      // Try multiple Edge TTS endpoints for better reliability
      final urls = [
        'https://speech.platform.bing.com/synthesize?language=zh-CN&text=$encodedText&format=audio-16khz-32kbitrate-mono-mp3&voice=zh-CN-XiaoxiaoNeural',
        'https://speech.platform.bing.com/synthesize?language=zh-CN&text=$encodedText&format=audio-16khz-32kbitrate-mono-mp3',
      ];

      AnxLog.info('Trying Edge TTS (China-accessible)');

      for (final url in urls) {
        try {
          await _audioPlayer.setAudioSource(
            AudioSource.uri(
              Uri.parse(url),
              headers: {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'audio/mpeg, audio/*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
              },
            ),
          );

          await _audioPlayer.play();
          AnxLog.info('Edge TTS playback successful');
          return true;
        } catch (e) {
          AnxLog.info('Edge TTS endpoint failed, trying next: $e');
          continue;
        }
      }

      AnxLog.info('All Edge TTS endpoints failed');
      return false;
    } catch (e) {
      AnxLog.info('Edge TTS failed: $e');
      return false;
    }
  }

  /// Try system TTS (primary service - works everywhere)
  Future<bool> _trySystemTtsFallback(String text, BuildContext? context) async {
    try {
      AnxLog.info('Attempting system TTS (primary service)');

      final quickTts = QuickTtsService();

      if (!quickTts.isSystemTtsAvailable) {
        AnxLog.info('System TTS not available, will try online services');
        return false;
      }

      return await quickTts.playSystemTts(text, context: context);
    } catch (e) {
      AnxLog.info('System TTS failed, will try online services: $e');
      return false;
    }
  }

  /// Look up a Chinese word or character in online dictionary
  Future<DictionaryEntry?> lookupChinese(String word) async {
    if (word.isEmpty) return null;

    // Check cache first
    if (_dictionaryCache.containsKey(word)) {
      AnxLog.info('Dictionary cache hit for "$word"');
      return _dictionaryCache[word];
    }

    // If in offline mode, return null if not in cache
    if (_isOfflineMode) {
      AnxLog.info('Dictionary lookup skipped in offline mode for "$word"');
      return null;
    }

    AnxLog.info('Looking up Chinese word online: "$word"');

    try {
      // Try MDBG API first (unofficial API, but reliable)
      final response = await _httpClient.get(
        Uri.parse(
          'https://api.mdbg.net/chinese/dictionary?word=$word&fields=traditional,simplified,pinyin,definitions',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>?;
        if (data != null &&
            data.containsKey('results') &&
            (data['results'] as List?)?.isNotEmpty == true) {
          final results = data['results'] as List;
          final result = results[0] as Map<String, dynamic>;

          // Prioritize simplified Chinese - use simplified form for both fields
          final simplified = result['simplified'] as String? ?? word;

          final entry = DictionaryEntry(
            traditional: simplified, // Use simplified for both fields
            simplified: simplified,
            pinyin: result['pinyin'] as String? ?? '',
            definitions:
                _parseDefinitions(result['definitions'] as String? ?? ''),
            hskLevel: result['hsk_level'] as int?,
            frequency: result['frequency'] as int?,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }

      // Try fallback to Wiktionary API
      final wiktResponse = await _httpClient.get(
        Uri.parse(
          'https://en.wiktionary.org/api/rest_v1/page/definition/$word',
        ),
      );

      if (wiktResponse.statusCode == 200) {
        final data = json.decode(wiktResponse.body) as Map<String, dynamic>?;
        if (data != null &&
            data.containsKey('zh') &&
            (data['zh'] as List?)?.isNotEmpty == true) {
          final zhList = data['zh'] as List;
          final zhData = zhList[0] as Map<String, dynamic>;

          // Extract definitions
          List<String> definitions = [];
          if (zhData.containsKey('definitions') &&
              (zhData['definitions'] as List?)?.isNotEmpty == true) {
            final definitionsList = zhData['definitions'] as List;
            definitions = definitionsList
                .map(
                  (def) =>
                      (def as Map<String, dynamic>)['definition']?.toString() ??
                      '',
                )
                .where((def) => def.isNotEmpty)
                .toList();
          }

          final entry = DictionaryEntry(
            traditional: word, // Use the same word for both fields
            simplified: word,
            pinyin: '', // Wiktionary doesn't provide pinyin in the API
            definitions: definitions,
            hskLevel: 0,
            frequency: 0,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }

      // Try third fallback to Chinese Tools API
      final ctResponse = await _httpClient.get(
        Uri.parse(
          'https://www.chinese-tools.com/tools/chinese-dictionary-api.html?q=$word',
        ),
      );

      if (ctResponse.statusCode == 200) {
        // Parse HTML response (simplified implementation)
        final html = ctResponse.body;

        // Extract pinyin and definition using regex (simplified)
        final pinyinMatch =
            RegExp(r'<span class="pinyin">(.*?)</span>').firstMatch(html);
        final defMatch =
            RegExp(r'<div class="definition">(.*?)</div>').firstMatch(html);

        if (pinyinMatch != null || defMatch != null) {
          final definitions =
              defMatch?.group(1)?.replaceAll(RegExp(r'<[^>]*>'), '') ?? '';

          final entry = DictionaryEntry(
            traditional: word, // Use the same word for both fields
            simplified: word,
            pinyin: pinyinMatch?.group(1) ?? '',
            definitions: _parseDefinitions(definitions),
            hskLevel: 0,
            frequency: 0,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }
    } catch (e) {
      AnxLog.severe('Error looking up word online: $e');
    }

    // If all online lookups fail, create a basic entry
    AnxLog.info(
      'No online dictionary entry found for "$word", creating basic entry',
    );
    final basicEntry = DictionaryEntry(
      traditional: word, // Use the same word for both fields
      simplified: word,
      pinyin: '',
      definitions: ['No definition available'],
      hskLevel: 0,
      frequency: 0,
    );

    // Cache the basic entry
    _dictionaryCache[word] = basicEntry;
    return basicEntry;
  }

  /// Parse definition string into a list of definitions
  List<String> _parseDefinitions(String definitionsStr) {
    if (definitionsStr.isEmpty) return ['No definition available'];
    return definitionsStr.split('/').where((def) => def.isNotEmpty).toList();
  }

  /// Get character information
  Future<CharacterInfo?> getCharacterInfo(String character) async {
    if (character.isEmpty) return null;

    // Check cache first
    if (_characterInfoCache.containsKey(character)) {
      AnxLog.info('Character info cache hit for "$character"');
      return _characterInfoCache[character];
    }

    // If in offline mode, return null if not in cache
    if (_isOfflineMode) {
      AnxLog.info(
        'Character info lookup skipped in offline mode for "$character"',
      );
      return null;
    }

    AnxLog.info('Getting character info for: "$character"');

    try {
      // Try to get basic character info
      final response = await _httpClient.get(
        Uri.parse('https://api.ctext.org/getcharacter?char=$character'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>?;

        if (data != null && data.containsKey('radical')) {
          // Create character info with basic data
          final characterInfo = CharacterInfo(
            character: character,
            radical: data['radical'] as String? ?? '',
            components: [character],
          );

          // Cache the result
          _characterInfoCache[character] = characterInfo;
          // Save cache periodically
          _saveCachedData();

          return characterInfo;
        }
      }
    } catch (e) {
      AnxLog.severe('Error getting character info: $e');
    }

    // If all lookups fail, create a basic entry
    final basicInfo = CharacterInfo(
      character: character,
      radical: '',
      components: [character],
    );

    // Cache the basic entry
    _characterInfoCache[character] = basicInfo;
    return basicInfo;
  }

  /// Find words containing a specific character
  Future<List<DictionaryEntry>> findWordsWithCharacter(String character) async {
    if (character.isEmpty) return [];

    // If in offline mode, only return cached entries
    if (_isOfflineMode) {
      AnxLog.info('Finding words with character in offline mode: "$character"');
      final cachedEntries = _dictionaryCache.values
          .where(
            (entry) =>
                entry.simplified.contains(character) ||
                entry.traditional.contains(character),
          )
          .toList();

      return cachedEntries;
    }

    AnxLog.info('Finding words with character online: "$character"');

    try {
      // Try MDBG API to find words containing the character
      final response = await _httpClient.get(
        Uri.parse(
          'https://api.mdbg.net/chinese/dictionary?word=$character&fields=words',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>?;
        if (data != null &&
            data.containsKey('words') &&
            (data['words'] as List?)?.isNotEmpty == true) {
          final words = data['words'] as List;
          final entries = <DictionaryEntry>[];

          for (final word in words) {
            final wordMap = word as Map<String, dynamic>;
            if (wordMap.containsKey('simplified') &&
                (wordMap['simplified'] as String?)?.contains(character) ==
                    true) {
              final entry = DictionaryEntry(
                traditional: wordMap['traditional'] as String? ??
                    wordMap['simplified'] as String? ??
                    '',
                simplified: wordMap['simplified'] as String? ?? '',
                pinyin: wordMap['pinyin'] as String? ?? '',
                definitions:
                    _parseDefinitions(wordMap['definitions'] as String? ?? ''),
                hskLevel: wordMap['hsk_level'] as int?,
                frequency: wordMap['frequency'] as int?,
              );
              entries.add(entry);

              // Cache the entry
              final simplifiedKey = wordMap['simplified'] as String? ?? '';
              if (simplifiedKey.isNotEmpty) {
                _dictionaryCache[simplifiedKey] = entry;
              }
            }
          }

          // Save cache periodically
          _saveCachedData();

          return entries;
        }
      }

      // If no results, return an empty list
      return [];
    } catch (e) {
      AnxLog.severe('Error finding words with character: $e');

      // In case of error, return cached entries containing the character
      final cachedEntries = _dictionaryCache.values
          .where(
            (entry) =>
                entry.simplified.contains(character) ||
                entry.traditional.contains(character),
          )
          .toList();

      return cachedEntries;
    }
  }

  /// Clear the cache
  Future<void> clearCache() async {
    _dictionaryCache.clear();
    _characterInfoCache.clear();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dictionaryCacheKey);
      await prefs.remove(_characterInfoCacheKey);
      AnxLog.info('Dictionary cache cleared');
    } catch (e) {
      AnxLog.severe('Error clearing dictionary cache: $e');
    }
  }

  /// Stop any ongoing audio playback
  Future<void> stopAudio() async {
    try {
      await _audioPlayer.stop();
      AnxLog.info('Audio playback stopped');
    } catch (e) {
      AnxLog.warning('Error stopping audio: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _audioPlayer.dispose();
    _saveCachedData();

    _httpClient.close();
  }
}
